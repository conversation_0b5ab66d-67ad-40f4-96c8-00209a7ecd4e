import type { Dispatch, SetStateAction } from "react";
import React, { useRef, useState } from "react";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ModalForm, ProFormText, ProFormTextArea, ProFormDatePicker, ProFormDigit, ProFormSelect } from "@ant-design/pro-form";
import { addDispatchInvoice } from "@/services/app/tenant/dispatch/dispatch-invoice";
import { message, Button, Card, Space, InputNumber, Input, Checkbox, Popconfirm } from "antd";
import { PlusOutlined, DeleteOutlined } from "@ant-design/icons";
import Util from "@/util";
import { ProList } from "@ant-design/pro-components";

const handleAdd = async (fields: API.DispatchInvoice) => {
  console.log(fields);
  return true;
  /* const hide = message.loading("Adding...", 0);
  const data = { ...fields };
  try {
    await addDispatchInvoice(data);
    message.success("Added successfully");
    return true;
  } catch (error: any) {
    Util.error("Adding failed, please try again!", error);
    return false;
  } finally {
    hide();
  } */
};

export type CreateFormProps = {
  values?: Partial<API.DispatchInvoice>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.DispatchInvoice) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>(null);
  const { modalVisible, handleModalVisible, onSubmit } = props;
  const [lineItems, setLineItems] = useState<API.DispatchInvoiceItem[]>([]);

  const addLineItem = () => {
    const newItem: API.DispatchInvoiceItem = {
      id: Date.now(), // temporary ID for new items
      name: "",
      description: "",
      qty: 1,
      price: 0,
      price_total: 0,
      taxable: 1,
      tax_percent: 0,
      tax_amount: 0,
      total: 0,
      sort_order: lineItems.length + 1,
    };
    setLineItems([...lineItems, newItem]);
  };

  const removeLineItem = (index: number) => {
    const newItems = lineItems.filter((_, i) => i !== index);
    setLineItems(newItems);
  };

  const updateLineItem = (index: number, field: keyof API.DispatchInvoiceItem, value: any) => {
    const newItems = [...lineItems];
    newItems[index] = { ...newItems[index], [field]: value };

    // Recalculate totals
    if (field === "qty" || field === "price") {
      const qty = field === "qty" ? value : newItems[index].qty || 0;
      const price = field === "price" ? value : newItems[index].price || 0;
      newItems[index].price_total = qty * price;
      newItems[index].total = newItems[index].price_total + (newItems[index].tax_amount || 0);
    }

    setLineItems(newItems);
  };

  return (
    <ModalForm
      title={"New Invoice"}
      width="1200px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      modalProps={{ maskClosable: false }}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 6 }}
      wrapperCol={{ span: 18 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd({
          ...value,
          items: lineItems,
        } as API.DispatchInvoice);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          setLineItems([]);
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      {/* Basic Invoice Information */}
      <ProFormText
        name="status"
        label="Status"
        placeholder="Enter invoice status"
        rules={[{ required: true, message: "Status is required" }]}
        initialValue="draft"
      />

      <ProFormDatePicker name="issue_date" label="Issue Date" rules={[{ required: true, message: "Issue date is required" }]} />

      <ProFormDatePicker name="valid_until" label="Valid Until" />

      <ProFormText name="payment_terms" label="Payment Terms" placeholder="e.g., Net 30" />

      <ProFormTextArea name="notes" label="Notes" placeholder="Additional notes for the invoice" fieldProps={{ rows: 3 }} />

      {/* Customer Information */}
      <ProFormText name="customer_company" label="Customer Company" placeholder="Company name" />

      <ProFormText
        name="customer_name"
        label="Customer Name"
        placeholder="Contact person name"
        rules={[{ required: true, message: "Customer name is required" }]}
      />

      <ProFormText
        name="customer_email"
        label="Customer Email"
        placeholder="<EMAIL>"
        rules={[{ type: "email", message: "Please enter a valid email" }]}
      />

      <ProFormText name="customer_phone" label="Customer Phone" placeholder="Phone number" />

      {/* Billing Address */}
      <ProFormText name="billing_company" label="Billing Company" placeholder="Billing company name" />

      <ProFormText name="billing_name" label="Billing Name" placeholder="Billing contact name" />

      <ProFormText
        name="billing_email"
        label="Billing Email"
        placeholder="<EMAIL>"
        rules={[{ type: "email", message: "Please enter a valid email" }]}
      />

      <ProFormText name="billing_phone" label="Billing Phone" placeholder="Billing phone number" />

      <ProFormText name="billing_street_number" label="Street Number" placeholder="123" />

      <ProFormText name="billing_street_name" label="Street Name" placeholder="Main Street" />

      <ProFormText name="billing_city" label="City" placeholder="City name" />

      <ProFormText name="billing_state" label="State" placeholder="State" />

      <ProFormDigit name="billing_zip" label="ZIP Code" placeholder="12345" fieldProps={{ precision: 0 }} />

      {/* Line Items Section */}
      <Card
        title="Line Items"
        style={{ marginTop: 16, marginBottom: 16 }}
        extra={
          <Button type="primary" icon={<PlusOutlined />} onClick={addLineItem}>
            Add Item
          </Button>
        }
      >
        <ProList
          dataSource={lineItems}
          metas={{
            title: {
              render: (_, item, index) => (
                <Input placeholder="Item name" value={item.name} onChange={(e) => updateLineItem(index, "name", e.target.value)} />
              ),
            },
            description: {
              render: (_, item, index) => (
                <Input placeholder="Description" value={item.description} onChange={(e) => updateLineItem(index, "description", e.target.value)} />
              ),
            },
            actions: {
              render: (_, item, index) => (
                <Space>
                  <InputNumber
                    placeholder="Qty"
                    min={0}
                    precision={2}
                    value={item.qty}
                    onChange={(value) => updateLineItem(index, "qty", value || 0)}
                    style={{ width: 80 }}
                  />
                  <InputNumber
                    placeholder="Price"
                    min={0}
                    precision={2}
                    value={item.price}
                    onChange={(value) => updateLineItem(index, "price", value || 0)}
                    style={{ width: 100 }}
                    formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
                    parser={(value) => parseFloat(value!.replace(/\$\s?|(,*)/g, "")) || 0}
                  />
                  <Checkbox checked={item.taxable === 1} onChange={(e) => updateLineItem(index, "taxable", e.target.checked ? 1 : 0)}>
                    Taxable
                  </Checkbox>
                  <span style={{ minWidth: 80, textAlign: "right" }}>${((item.qty || 0) * (item.price || 0)).toFixed(2)}</span>
                  <Popconfirm title="Are you sure you want to remove this item?" onConfirm={() => removeLineItem(index)} okText="Yes" cancelText="No">
                    <Button type="text" danger icon={<DeleteOutlined />} size="small" />
                  </Popconfirm>
                </Space>
              ),
            },
          }}
          pagination={false}
        />

        {lineItems.length === 0 && (
          <div style={{ textAlign: "center", padding: "20px", color: "#999" }}>No line items added yet. Click "Add Item" to get started.</div>
        )}

        {lineItems.length > 0 && (
          <div style={{ marginTop: 16, textAlign: "right" }}>
            <Space direction="vertical" size="small">
              <div>
                <strong>Subtotal: ${lineItems.reduce((sum, item) => sum + (item.qty || 0) * (item.price || 0), 0).toFixed(2)}</strong>
              </div>
              <div>
                <strong>Total Items: {lineItems.length}</strong>
              </div>
            </Space>
          </div>
        )}
      </Card>
    </ModalForm>
  );
};

export default CreateForm;
