import type { Dispatch, SetStateAction } from "react";
import React, { useRef } from "react";
import type { ProFormInstance } from "@ant-design/pro-form";
import { ModalForm, ProFormText } from "@ant-design/pro-form";
import { addDispatchInvoice } from "@/services/app/tenant/dispatch/dispatch-invoice";
import { message } from "antd";
import Util from "@/util";
import { ProFormColorPicker } from "@ant-design/pro-components";

const handleAdd = async (fields: API.DispatchInvoice) => {
  const hide = message.loading("Adding...", 0);
  const data = { ...fields };
  try {
    await addDispatchInvoice(data);
    message.success("Added successfully");
    return true;
  } catch (error: any) {
    Util.error("Adding failed, please try again!", error);
    return false;
  } finally {
    hide();
  }
};

export type CreateFormProps = {
  values?: Partial<API.DispatchInvoice>;
  modalVisible: boolean;
  handleModalVisible: Dispatch<SetStateAction<boolean>>;
  onSubmit?: (formData: API.DispatchInvoice) => Promise<boolean | void>;
};

const CreateForm: React.FC<CreateFormProps> = (props) => {
  const formRef = useRef<ProFormInstance>(null);
  const { modalVisible, handleModalVisible, onSubmit } = props;
  return (
    <ModalForm
      title={"New Invoice"}
      width="1200px"
      open={modalVisible}
      onOpenChange={handleModalVisible}
      modalProps={{ maskClosable: false }}
      layout="horizontal"
      labelAlign="left"
      labelCol={{ span: 7 }}
      wrapperCol={{ span: 17 }}
      formRef={formRef}
      onFinish={async (value) => {
        const success = await handleAdd({
          ...value,
          color_hex: typeof value.color_hex === "string" ? value.color_hex : value.color_hex.toHexString(),
        } as API.DispatchInvoice);
        if (success) {
          if (formRef.current) formRef.current.resetFields();
          if (onSubmit) await onSubmit(value);
        }
      }}
    >
      <ProFormText
        rules={[
          {
            required: true,
            message: "Status Name is required",
          },
        ]}
        width="md"
        name="status"
        label="Status"
        required
      />

      <ProFormColorPicker name="color_hex" label="Color" />
    </ModalForm>
  );
};

export default CreateForm;
